# 🔥 تعليمات عاجلة للوكيل التالي

## 🎯 **المهمة الأساسية:**
إكمال تفعيل النماذج المتخصصة المنفصلة للحرائق

## ✅ **ما تم إنجازه:**
1. **إصلاح مشكلة "الحفظ الجزئي"**: أضفت تشخيص شامل في `saveAgriculturalFireDetails()`
2. **تعديل `updateToReconnaissance`**: الآن تستدعي النماذج المتخصصة للحرائق
3. **API يعمل بشكل مثالي**: اختبرت `save_agricultural_fire_details` - يحفظ بنجاح

## ❌ **المطلوب إكماله:**

### 1. **إضافة الدوال المفقودة** (عاجل)
في `dpcdz/templates/coordination_center/daily_interventions.html` أضف:

```javascript
// دوال إضافة الضحايا للنماذج المتخصصة
function addAgriculturalCasualty(type) {
    // نسخ من دالة addCasualty الموجودة وتعديلها للنموذج المتخصص
}

function addBuildingFireCasualty(type) {
    // نسخ من دالة addCasualty الموجودة وتعديلها للنموذج المتخصص
}

// دوال إخفاء النماذج
function hideAgriculturalFireForm() {
    document.getElementById('agricultural-fire-form').style.display = 'none';
}

function hideBuildingFireForm() {
    document.getElementById('building-fire-form').style.display = 'none';
}
```

### 2. **تحديث دالة الحفظ** (مهم)
في `saveAgriculturalFireDetails()` غيّر IDs العناصر من النموذج المؤقت إلى المنفصل:

```javascript
// بدلاً من:
document.getElementById('crop-fire-points')?.value
// استخدم:
document.getElementById('fire-sources-count')?.value

// بدلاً من:
document.getElementById('crop-wind-direction')?.value  
// استخدم:
document.getElementById('agricultural-wind-direction')?.value
```

### 3. **الاختبار**
1. إنشاء تدخل حريق محاصيل
2. الضغط على "عملية التعرف" 
3. يجب أن يفتح النموذج المتخصص المنفصل
4. ملء البيانات والحفظ
5. التأكد من عدم ظهور "حفظ جزئي"

## 📁 **الملفات المهمة:**
- `dpcdz/templates/coordination_center/daily_interventions.html` (التعديل الرئيسي)
- `dpcdz/templates/coordination_center/intervention_forms/agricultural_fire_form.html` (جاهز)
- `dpcdz/templates/coordination_center/intervention_forms/building_fire_form.html` (جاهز)

## 🧪 **للاختبار:**
الخادم يعمل على: `http://127.0.0.1:8000/coordination-center/daily-interventions/`

## 🎯 **الهدف:**
نماذج متخصصة منفصلة تعمل مثل حوادث المرور تماماً، بدون "حفظ جزئي".

---

**🔥 هذا العمل مهم جداً لتنظيم النظام! النماذج المتخصصة موجودة ومتقنة، تحتاج فقط ربطها بـ JavaScript.**
